# UserGroupController 前端接口清单

## 📋 接口概览

**基础路径**: `/api/usergroup`  
**Content-Type**: `application/json`  
**响应格式**: 统一使用 `ResultDTO<T>` 包装

---

## 🔧 通用数据结构

### ResultDTO<T> - 统一响应格式
```typescript
interface ResultDTO<T> {
  code: number;        // 状态码：200=成功，500=失败，404=未找到，429=限流
  message: string;     // 响应消息
  data: T;            // 业务数据
  timestamp: number;   // 时间戳
}
```

### UserGroupDTO - 用户分组数据
```typescript
interface UserGroupDTO {
  userId: string;      // 用户ID
  source: string;      // 数据来源（如：dx、lt、yd）
  groupIds: string[];  // 分组ID数组
}
```

### GroupInfoDTO - 分组信息数据
```typescript
interface GroupInfoDTO {
  groupId: string;     // 分组ID
  businessId: string;  // 业务ID
  groupName: string;   // 分组名称
  description: string; // 分组描述
}
```

---

## 🚀 核心业务接口

### 1. 获取用户分组信息
```http
GET /api/usergroup/{source}/{userId}
```

**参数说明**:
- `source`: 数据来源（路径参数，必填）
- `userId`: 用户ID（路径参数，必填）

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": ["group001", "group002", "group003"],
  "timestamp": 1704067200000
}
```

**前端调用示例**:
```typescript
const getUserGroups = async (source: string, userId: string) => {
  const response = await fetch(`/api/usergroup/${source}/${userId}`);
  return await response.json() as ResultDTO<string[]>;
};
```

---

### 2. 获取详细用户分组信息
```http
GET /api/usergroup/{source}/{userId}/detail
```

**参数说明**:
- `source`: 数据来源（路径参数，必填）
- `userId`: 用户ID（路径参数，必填）

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "group001": {
      "groupId": "group001",
      "businessId": "biz001",
      "groupName": "VIP用户组",
      "description": "高级会员用户分组"
    },
    "group002": {
      "groupId": "group002", 
      "businessId": "biz002",
      "groupName": "普通用户组",
      "description": "普通会员用户分组"
    }
  },
  "timestamp": 1704067200000
}
```

**前端调用示例**:
```typescript
const getUserGroupDetails = async (source: string, userId: string) => {
  const response = await fetch(`/api/usergroup/${source}/${userId}/detail`);
  return await response.json() as ResultDTO<Record<string, GroupInfoDTO>>;
};
```

---

### 3. 保存用户分组信息
```http
POST /api/usergroup
```

**请求体**:
```json
{
  "userId": "user123",
  "source": "dx",
  "groupIds": ["group001", "group002"]
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Success", 
  "data": "操作成功",
  "timestamp": 1704067200000
}
```

**前端调用示例**:
```typescript
const saveUserGroup = async (userGroup: UserGroupDTO) => {
  const response = await fetch('/api/usergroup', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(userGroup)
  });
  return await response.json() as ResultDTO<string>;
};
```

---

### 4. 保存分组信息
```http
POST /api/usergroup/group
```

**请求体**:
```json
{
  "groupId": "group001",
  "businessId": "biz001", 
  "groupName": "VIP用户组",
  "description": "高级会员用户分组"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": "操作成功", 
  "timestamp": 1704067200000
}
```

**前端调用示例**:
```typescript
const saveGroupInfo = async (groupInfo: GroupInfoDTO) => {
  const response = await fetch('/api/usergroup/group', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(groupInfo)
  });
  return await response.json() as ResultDTO<string>;
};
```

---

## 📊 管理功能接口

### 5. 分页查询用户分组列表
```http
GET /api/usergroup/list?source={source}&userId={userId}&page={page}&pageSize={pageSize}
```

**查询参数**:
- `source`: 数据来源（可选）
- `userId`: 用户ID（可选）
- `page`: 页码，默认1
- `pageSize`: 每页大小，默认10，最大100

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "items": [
      {
        "id": 1,
        "userId": "user123",
        "source": "dx", 
        "groupIds": ["group001", "group002"],
        "createdAt": "2024-01-01T10:00:00",
        "updatedAt": "2024-01-01T10:00:00"
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10
  },
  "timestamp": 1704067200000
}
```

**前端调用示例**:
```typescript
interface PaginationParams {
  source?: string;
  userId?: string;
  page?: number;
  pageSize?: number;
}

const getUserGroupList = async (params: PaginationParams) => {
  const queryString = new URLSearchParams(
    Object.entries(params).filter(([_, v]) => v !== undefined)
  ).toString();
  
  const response = await fetch(`/api/usergroup/list?${queryString}`);
  return await response.json() as ResultDTO<{
    items: any[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }>;
};
```

---

### 6. 删除用户分组
```http
DELETE /api/usergroup/{id}
```

**参数说明**:
- `id`: 用户分组关系ID（路径参数，必填）

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": "删除成功",
  "timestamp": 1704067200000
}
```

**前端调用示例**:
```typescript
const deleteUserGroup = async (id: number) => {
  const response = await fetch(`/api/usergroup/${id}`, {
    method: 'DELETE'
  });
  return await response.json() as ResultDTO<string>;
};
```

---

## 🔍 查询功能接口

### 7. 获取所有分组
```http
GET /api/usergroup/groups
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "groupId": "group001",
      "businessId": "biz001",
      "groupName": "VIP用户组", 
      "description": "高级会员用户分组"
    },
    {
      "groupId": "group002",
      "businessId": "biz002", 
      "groupName": "普通用户组",
      "description": "普通会员用户分组"
    }
  ],
  "timestamp": 1704067200000
}
```

**前端调用示例**:
```typescript
const getAllGroups = async () => {
  const response = await fetch('/api/usergroup/groups');
  return await response.json() as ResultDTO<GroupInfoDTO[]>;
};
```

---

### 8. 按运营商获取分组
```http
GET /api/usergroup/groups/{source}
```

**参数说明**:
- `source`: 运营商/数据来源（路径参数，必填）

**响应示例**:
```json
{
  "code": 200,
  "message": "Success", 
  "data": [
    {
      "groupId": "dx_group001",
      "businessId": "dx_biz001",
      "groupName": "电信VIP用户组",
      "description": "电信高级会员用户分组"
    }
  ],
  "timestamp": 1704067200000
}
```

**前端调用示例**:
```typescript
const getGroupsBySource = async (source: string) => {
  const response = await fetch(`/api/usergroup/groups/${source}`);
  return await response.json() as ResultDTO<GroupInfoDTO[]>;
};
```

---

### 9. 健康检查
```http
GET /api/usergroup/health
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": "用户分组服务运行正常",
  "timestamp": 1704067200000
}
```

**前端调用示例**:
```typescript
const healthCheck = async () => {
  const response = await fetch('/api/usergroup/health');
  return await response.json() as ResultDTO<string>;
};
```

---

## ⚠️ 错误处理

### 常见错误码
- `200`: 成功
- `404`: 资源未找到
- `429`: 请求频率超限
- `500`: 服务器内部错误

### 错误响应示例
```json
{
  "code": 500,
  "message": "获取用户分组失败：数据库连接超时",
  "data": null,
  "timestamp": 1704067200000
}
```

### 前端错误处理示例
```typescript
const handleApiCall = async <T>(apiCall: () => Promise<ResultDTO<T>>) => {
  try {
    const result = await apiCall();
    if (result.code === 200) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('API调用失败:', error);
    throw error;
  }
};
```

---

## 🎯 前端集成建议

### 1. 创建API服务类
```typescript
class UserGroupService {
  private baseUrl = '/api/usergroup';
  
  async getUserGroups(source: string, userId: string) {
    return handleApiCall(() => 
      fetch(`${this.baseUrl}/${source}/${userId}`).then(r => r.json())
    );
  }
  
  async saveUserGroup(userGroup: UserGroupDTO) {
    return handleApiCall(() =>
      fetch(this.baseUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userGroup)
      }).then(r => r.json())
    );
  }
  
  // ... 其他方法
}
```

### 2. 状态管理集成
```typescript
// 使用 Pinia/Vuex/Redux 等状态管理
const useUserGroupStore = () => {
  const userGroups = ref<string[]>([]);
  const loading = ref(false);
  
  const fetchUserGroups = async (source: string, userId: string) => {
    loading.value = true;
    try {
      userGroups.value = await userGroupService.getUserGroups(source, userId);
    } finally {
      loading.value = false;
    }
  };
  
  return { userGroups, loading, fetchUserGroups };
};
```

---

## 📝 注意事项

1. **异步处理**: 部分接口返回 `CompletableFuture`，前端需要正常处理异步响应
2. **限流保护**: 接口有限流保护，建议前端实现重试机制
3. **缓存策略**: 系统有多级缓存，数据更新可能有延迟
4. **参数校验**: 所有必填参数都有后端校验，前端也应做相应校验
5. **错误处理**: 建议统一处理错误响应，提供用户友好的错误提示

---

*文档生成时间: 2025-01-04*  
*接口版本: v1.0*  
*维护者: Claude 4.0 sonnet* 🐱
