#!/bin/bash

# 测试时间戳类型转换修复
echo "🔧 测试时间戳类型转换修复"
echo "================================"

BASE_URL="http://127.0.0.1:7003"

echo ""
echo "📊 1. 测试仪表盘统计接口..."
STATISTICS_RESPONSE=$(curl -s "${BASE_URL}/api/dashboard/statistics")
STATISTICS_CODE=$(echo $STATISTICS_RESPONSE | grep -o '"code":[0-9]*' | cut -d':' -f2)

if [ "$STATISTICS_CODE" = "200" ]; then
    echo "✅ 仪表盘统计接口正常"
    echo "   响应: $(echo $STATISTICS_RESPONSE | head -c 100)..."
else
    echo "❌ 仪表盘统计接口异常"
    echo "   错误: $STATISTICS_RESPONSE"
fi

echo ""
echo "📈 2. 测试活动记录接口..."
ACTIVITIES_RESPONSE=$(curl -s "${BASE_URL}/api/dashboard/activities?limit=5")
ACTIVITIES_CODE=$(echo $ACTIVITIES_RESPONSE | grep -o '"code":[0-9]*' | cut -d':' -f2)

if [ "$ACTIVITIES_CODE" = "200" ]; then
    echo "✅ 活动记录接口正常"
    echo "   响应: $(echo $ACTIVITIES_RESPONSE | head -c 100)..."
else
    echo "❌ 活动记录接口异常"
    echo "   错误: $ACTIVITIES_RESPONSE"
fi

echo ""
echo "📊 3. 测试运营商分布接口..."
OPERATORS_RESPONSE=$(curl -s "${BASE_URL}/api/dashboard/operators")
OPERATORS_CODE=$(echo $OPERATORS_RESPONSE | grep -o '"code":[0-9]*' | cut -d':' -f2)

if [ "$OPERATORS_CODE" = "200" ]; then
    echo "✅ 运营商分布接口正常"
    echo "   响应: $(echo $OPERATORS_RESPONSE | head -c 100)..."
else
    echo "❌ 运营商分布接口异常"
    echo "   错误: $OPERATORS_RESPONSE"
fi

echo ""
echo "📈 4. 测试用户增长趋势接口..."
TRENDS_RESPONSE=$(curl -s "${BASE_URL}/api/dashboard/trends?days=7")
TRENDS_CODE=$(echo $TRENDS_RESPONSE | grep -o '"code":[0-9]*' | cut -d':' -f2)

if [ "$TRENDS_CODE" = "200" ]; then
    echo "✅ 用户增长趋势接口正常"
    echo "   响应: $(echo $TRENDS_RESPONSE | head -c 100)..."
else
    echo "❌ 用户增长趋势接口异常"
    echo "   错误: $TRENDS_RESPONSE"
fi

echo ""
echo "🔍 5. 测试用户分组列表接口..."
USERGROUP_LIST_RESPONSE=$(curl -s "${BASE_URL}/api/usergroup/list?page=1&pageSize=5")
USERGROUP_LIST_CODE=$(echo $USERGROUP_LIST_RESPONSE | grep -o '"code":[0-9]*' | cut -d':' -f2)

if [ "$USERGROUP_LIST_CODE" = "200" ]; then
    echo "✅ 用户分组列表接口正常"
    echo "   响应: $(echo $USERGROUP_LIST_RESPONSE | head -c 100)..."
else
    echo "❌ 用户分组列表接口异常"
    echo "   错误: $USERGROUP_LIST_RESPONSE"
fi

echo ""
echo "🏥 6. 测试健康检查接口..."
HEALTH_RESPONSE=$(curl -s "${BASE_URL}/api/usergroup/health")
HEALTH_CODE=$(echo $HEALTH_RESPONSE | grep -o '"code":[0-9]*' | cut -d':' -f2)

if [ "$HEALTH_CODE" = "200" ]; then
    echo "✅ 健康检查接口正常"
    echo "   响应: $HEALTH_RESPONSE"
else
    echo "❌ 健康检查接口异常"
    echo "   错误: $HEALTH_RESPONSE"
fi

echo ""
echo "📊 7. 测试系统监控接口..."
MONITOR_RESPONSE=$(curl -s "${BASE_URL}/api/monitor/health")
MONITOR_CODE=$(echo $MONITOR_RESPONSE | grep -o '"code":[0-9]*' | cut -d':' -f2)

if [ "$MONITOR_CODE" = "200" ]; then
    echo "✅ 系统监控接口正常"
    echo "   响应: $(echo $MONITOR_RESPONSE | head -c 100)..."
else
    echo "❌ 系统监控接口异常"
    echo "   错误: $MONITOR_RESPONSE"
fi

echo ""
echo "🎯 8. 汇总测试结果..."
SUCCESS_COUNT=0
TOTAL_COUNT=7

# 统计成功的接口数量
[ "$STATISTICS_CODE" = "200" ] && ((SUCCESS_COUNT++))
[ "$ACTIVITIES_CODE" = "200" ] && ((SUCCESS_COUNT++))
[ "$OPERATORS_CODE" = "200" ] && ((SUCCESS_COUNT++))
[ "$TRENDS_CODE" = "200" ] && ((SUCCESS_COUNT++))
[ "$USERGROUP_LIST_CODE" = "200" ] && ((SUCCESS_COUNT++))
[ "$HEALTH_CODE" = "200" ] && ((SUCCESS_COUNT++))
[ "$MONITOR_CODE" = "200" ] && ((SUCCESS_COUNT++))

echo "📈 测试结果统计:"
echo "   成功: $SUCCESS_COUNT/$TOTAL_COUNT"
echo "   成功率: $((SUCCESS_COUNT * 100 / TOTAL_COUNT))%"

if [ $SUCCESS_COUNT -eq $TOTAL_COUNT ]; then
    echo ""
    echo "🎉 所有接口测试通过！时间戳类型转换修复成功！"
    echo "✅ 修复内容:"
    echo "   1. DashboardRepository 中的 LocalDateTime.class → Timestamp.class"
    echo "   2. UserGroupRepository 中的时间戳查询修复"
    echo "   3. 正确的 Timestamp 到 LocalDateTime 转换"
else
    echo ""
    echo "⚠️  部分接口仍有问题，需要进一步检查"
    echo "❌ 失败的接口数量: $((TOTAL_COUNT - SUCCESS_COUNT))"
fi

echo ""
echo "🔧 修复技术细节:"
echo "   问题: java.sql.Timestamp cannot be cast to java.time.LocalDateTime"
echo "   原因: jOOQ 中直接使用 LocalDateTime.class 类型转换"
echo "   解决: 使用 Timestamp.class 获取后调用 toLocalDateTime() 转换"
echo ""
echo "✅ 时间戳类型转换修复测试完成"
