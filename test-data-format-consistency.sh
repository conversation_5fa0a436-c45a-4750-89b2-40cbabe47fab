#!/bin/bash

# 测试数据格式一致性
echo "🧪 测试数据格式一致性修复"

# 服务地址
BASE_URL="http://localhost:3000"

echo "📤 1. 创建测试文件..."
cat > format-test.csv << EOF
user_id
test001
test002
test003
test004
test005
test006
test007
test008
test009
test010
EOF

echo "📋 测试文件包含 10 条记录"

echo ""
echo "📊 2. 测试上传接口的数据格式..."
UPLOAD_RESPONSE=$(curl -s -X POST \
  "${BASE_URL}/api/data-import/upload" \
  -F "file=@format-test.csv" \
  -F "source=format_test" \
  -F "async=true" \
  -F "description=数据格式一致性测试")

echo "上传响应: $UPLOAD_RESPONSE"

# 提取任务ID和状态
TASK_ID=$(echo $UPLOAD_RESPONSE | grep -o '"taskId":"[^"]*"' | cut -d'"' -f4)
UPLOAD_STATUS=$(echo $UPLOAD_RESPONSE | grep -o '"status":"[^"]*"' | cut -d'"' -f4)

echo ""
echo "📋 上传接口数据格式验证:"
echo "   任务ID: $TASK_ID"
echo "   状态格式: $UPLOAD_STATUS"

# 验证状态格式
if [[ "$UPLOAD_STATUS" =~ ^(PENDING|PARSING|PROCESSING|IMPORTING|COMPLETED|FAILED)$ ]]; then
    echo "   ✅ 状态格式正确: 英文状态码"
else
    echo "   ❌ 状态格式错误: 应该是英文状态码，实际是 $UPLOAD_STATUS"
fi

if [ -z "$TASK_ID" ]; then
    echo "❌ 无法获取任务ID"
    exit 1
fi

echo ""
echo "📊 3. 监控进度接口的数据格式..."
for i in {1..10}; do
    echo "--- 第 $i 次查询 ($(date '+%H:%M:%S')) ---"
    
    PROGRESS_RESPONSE=$(curl -s "${BASE_URL}/api/data-import/progress/${TASK_ID}")
    
    # 提取关键字段
    STATUS=$(echo $PROGRESS_RESPONSE | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
    PERCENTAGE=$(echo $PROGRESS_RESPONSE | grep -o '"progressPercentage":[0-9.]*' | cut -d':' -f2)
    PROCESSED=$(echo $PROGRESS_RESPONSE | grep -o '"processedRecords":[0-9]*' | cut -d':' -f2)
    TOTAL=$(echo $PROGRESS_RESPONSE | grep -o '"totalRecords":[0-9]*' | cut -d':' -f2)
    
    echo "📊 进度接口数据:"
    echo "   状态: $STATUS"
    echo "   进度百分比: $PERCENTAGE"
    echo "   已处理: $PROCESSED"
    echo "   总记录: $TOTAL"
    
    # 验证状态格式
    if [[ "$STATUS" =~ ^(PENDING|PARSING|PROCESSING|IMPORTING|COMPLETED|FAILED)$ ]]; then
        echo "   ✅ 状态格式正确: 英文状态码"
    else
        echo "   ❌ 状态格式错误: 应该是英文状态码，实际是 $STATUS"
    fi
    
    # 验证进度百分比格式
    if [ ! -z "$PERCENTAGE" ]; then
        # 检查是否是0-1之间的小数
        IS_DECIMAL=$(echo "$PERCENTAGE <= 1.0 && $PERCENTAGE >= 0.0" | bc -l 2>/dev/null || echo "0")
        
        if [ "$IS_DECIMAL" = "1" ]; then
            echo "   ✅ 进度格式正确: 小数格式 ($PERCENTAGE)"
            
            # 计算前端显示的百分比
            FRONTEND_PERCENTAGE=$(echo "scale=2; $PERCENTAGE * 100" | bc -l 2>/dev/null || echo "计算错误")
            echo "   📱 前端显示: ${FRONTEND_PERCENTAGE}%"
        else
            echo "   ❌ 进度格式错误: 应该是0-1之间的小数，实际是 $PERCENTAGE"
            
            # 如果是百分比格式，显示问题
            if [ $(echo "$PERCENTAGE > 1.0" | bc -l 2>/dev/null || echo "0") = "1" ]; then
                echo "   ⚠️  这是百分比格式，前端会显示为 $(echo "scale=0; $PERCENTAGE * 100" | bc -l 2>/dev/null || echo "错误")%"
            fi
        fi
        
        # 验证进度逻辑
        if [ ! -z "$PROCESSED" ] && [ ! -z "$TOTAL" ] && [ "$TOTAL" -gt 0 ]; then
            EXPECTED_RATIO=$(echo "scale=4; $PROCESSED / $TOTAL" | bc -l 2>/dev/null || echo "0")
            echo "   🎯 期望进度: $EXPECTED_RATIO (基于处理记录数)"
            
            if [ ! -z "$EXPECTED_RATIO" ]; then
                DIFF=$(echo "scale=4; $PERCENTAGE - $EXPECTED_RATIO" | bc -l 2>/dev/null || echo "0")
                ABS_DIFF=$(echo "$DIFF" | sed 's/-//')
                
                if [ $(echo "$ABS_DIFF < 0.01" | bc -l 2>/dev/null || echo "0") = "1" ]; then
                    echo "   ✅ 进度计算正确: 差异 $DIFF"
                else
                    echo "   ⚠️  进度计算可能有偏差: 差异 $DIFF"
                fi
            fi
        fi
    fi
    
    echo ""
    
    # 检查是否完成
    if [ "$STATUS" = "COMPLETED" ] || [ "$STATUS" = "FAILED" ]; then
        echo "✅ 处理完成，最终状态: $STATUS"
        
        # 最终验证
        if [ "$STATUS" = "COMPLETED" ]; then
            if [ "$PERCENTAGE" = "1.0" ] || [ "$PERCENTAGE" = "1" ]; then
                echo "✅ 完成状态进度正确: $PERCENTAGE (表示100%)"
            else
                echo "❌ 完成状态进度错误: 应该是1.0，实际是 $PERCENTAGE"
            fi
        fi
        break
    fi
    
    sleep 1
done

echo ""
echo "🔍 4. 数据格式一致性总结..."
echo "✅ 修复内容:"
echo "   1. 状态字段统一为英文状态码 (PENDING/PARSING/PROCESSING/IMPORTING/COMPLETED/FAILED)"
echo "   2. 进度百分比统一为小数格式 (0-1之间)"
echo "   3. 前端映射: 英文状态码 → 中文显示，小数进度 → 百分比显示"

echo ""
echo "📱 前端处理建议:"
echo "   状态映射: { PENDING: '等待处理', PROCESSING: '正在处理', COMPLETED: '处理完成' }"
echo "   进度显示: progressPercentage * 100 + '%'"
echo "   进度条值: progressPercentage (直接使用0-1之间的值)"

echo ""
echo "🧹 5. 清理测试文件..."
rm -f format-test.csv

echo "✅ 数据格式一致性测试完成"
