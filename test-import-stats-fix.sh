#!/bin/bash

# 测试导入统计数据修复
echo "🧪 测试导入统计数据逻辑修复"

# 服务地址
BASE_URL="http://localhost:3000"

echo "📊 1. 测试修复前后的数据一致性..."

echo "📤 获取导入统计数据..."
STATS_RESPONSE=$(curl -s "${BASE_URL}/api/dashboard/import-stats")

echo "导入统计响应: $STATS_RESPONSE"

# 提取关键数据
TOTAL_IMPORTS=$(echo $STATS_RESPONSE | grep -o '"totalImports":[0-9]*' | cut -d':' -f2)
TODAY_IMPORTS=$(echo $STATS_RESPONSE | grep -o '"todayImports":[0-9]*' | cut -d':' -f2)
SUCCESSFUL_IMPORTS=$(echo $STATS_RESPONSE | grep -o '"successfulImports":[0-9]*' | cut -d':' -f2)
FAILED_IMPORTS=$(echo $STATS_RESPONSE | grep -o '"failedImports":[0-9]*' | cut -d':' -f2)
SUCCESS_RATE=$(echo $STATS_RESPONSE | grep -o '"successRate":[0-9.]*' | cut -d':' -f2)

echo ""
echo "📊 导入统计数据分析:"
echo "   总导入次数: $TOTAL_IMPORTS"
echo "   今日导入次数: $TODAY_IMPORTS"
echo "   成功导入次数: $SUCCESSFUL_IMPORTS"
echo "   失败导入次数: $FAILED_IMPORTS"
echo "   成功率: $SUCCESS_RATE%"

echo ""
echo "🔍 数据逻辑验证:"

# 验证1: 今日导入次数不应该大于总导入次数
if [ ! -z "$TOTAL_IMPORTS" ] && [ ! -z "$TODAY_IMPORTS" ]; then
    if [ "$TODAY_IMPORTS" -le "$TOTAL_IMPORTS" ]; then
        echo "   ✅ 今日导入次数 ($TODAY_IMPORTS) ≤ 总导入次数 ($TOTAL_IMPORTS) - 逻辑正确"
    else
        echo "   ❌ 今日导入次数 ($TODAY_IMPORTS) > 总导入次数 ($TOTAL_IMPORTS) - 逻辑错误"
    fi
else
    echo "   ⚠️  无法获取导入次数数据"
fi

# 验证2: 成功+失败应该等于总数
if [ ! -z "$TOTAL_IMPORTS" ] && [ ! -z "$SUCCESSFUL_IMPORTS" ] && [ ! -z "$FAILED_IMPORTS" ]; then
    CALCULATED_TOTAL=$((SUCCESSFUL_IMPORTS + FAILED_IMPORTS))
    if [ "$CALCULATED_TOTAL" -eq "$TOTAL_IMPORTS" ]; then
        echo "   ✅ 成功 ($SUCCESSFUL_IMPORTS) + 失败 ($FAILED_IMPORTS) = 总数 ($TOTAL_IMPORTS) - 计算正确"
    else
        echo "   ⚠️  成功 ($SUCCESSFUL_IMPORTS) + 失败 ($FAILED_IMPORTS) = $CALCULATED_TOTAL ≠ 总数 ($TOTAL_IMPORTS)"
        echo "       这可能是因为有处理中的任务"
    fi
fi

# 验证3: 成功率计算
if [ ! -z "$TOTAL_IMPORTS" ] && [ ! -z "$SUCCESSFUL_IMPORTS" ] && [ "$TOTAL_IMPORTS" -gt 0 ]; then
    CALCULATED_RATE=$(echo "scale=1; $SUCCESSFUL_IMPORTS * 100 / $TOTAL_IMPORTS" | bc -l 2>/dev/null || echo "计算错误")
    if [ ! -z "$CALCULATED_RATE" ] && [ "$CALCULATED_RATE" != "计算错误" ]; then
        echo "   🎯 计算的成功率: ${CALCULATED_RATE}%"
        echo "   📊 系统返回成功率: ${SUCCESS_RATE}%"
        
        # 检查差异
        RATE_DIFF=$(echo "scale=1; $SUCCESS_RATE - $CALCULATED_RATE" | bc -l 2>/dev/null || echo "0")
        ABS_DIFF=$(echo "$RATE_DIFF" | sed 's/-//')
        
        if [ $(echo "$ABS_DIFF < 0.1" | bc -l 2>/dev/null || echo "0") = "1" ]; then
            echo "   ✅ 成功率计算正确，差异: ${RATE_DIFF}%"
        else
            echo "   ⚠️  成功率计算可能有偏差，差异: ${RATE_DIFF}%"
        fi
    fi
fi

echo ""
echo "📈 2. 数据源验证..."
echo "修复说明:"
echo "   修复前: 总导入次数来自 metrics（应用级计数器，可能重置）"
echo "   修复后: 总导入次数来自数据库（真实历史数据）"
echo "   今日导入: 始终来自数据库（按日期过滤）"

echo ""
echo "📊 3. 预期修复效果:"
echo "   ✅ 总导入次数 ≥ 今日导入次数"
echo "   ✅ 数据来源统一（都来自数据库）"
echo "   ✅ 统计逻辑一致"
echo "   ✅ 避免应用重启导致的计数器重置问题"

echo ""
echo "🔍 4. 问题诊断建议:"
if [ ! -z "$TOTAL_IMPORTS" ] && [ ! -z "$TODAY_IMPORTS" ]; then
    if [ "$TODAY_IMPORTS" -gt "$TOTAL_IMPORTS" ]; then
        echo "   如果仍然出现今日>总数的问题，可能原因:"
        echo "   1. 数据库中有重复记录"
        echo "   2. 时区设置问题"
        echo "   3. 数据同步延迟"
        echo "   4. SQL查询逻辑需要进一步优化"
    else
        echo "   ✅ 数据逻辑正常，修复成功"
    fi
fi

echo ""
echo "📋 5. 数据库验证建议:"
echo "   可以直接查询数据库验证:"
echo "   SELECT COUNT(*) as total_imports FROM data_import_log;"
echo "   SELECT COUNT(*) as today_imports FROM data_import_log WHERE DATE(created_at) = CURDATE();"

echo ""
echo "✅ 导入统计数据修复验证完成"
