package com.iptv.flux.service.controller;

import com.iptv.flux.common.constants.UserGroupConstants;
import com.iptv.flux.common.dto.ResultDTO;
import com.iptv.flux.service.schedule.CacheWarmupJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(UserGroupConstants.API_CACHE_PATH)
@RequiredArgsConstructor
@Slf4j
public class CacheManagementController {

    private final Scheduler scheduler;
    private final JobDetail cacheWarmupJobDetail;

    @PostMapping("/warmup")
    public ResultDTO<String> triggerWarmup(@RequestParam(value = "fullLoad", defaultValue = "false") boolean fullLoad) {
        try {
            JobDataMap dataMap = new JobDataMap();
            dataMap.put("fullLoad", fullLoad);
            dataMap.put("manualTrigger", true);

            JobDetail jobDetail = JobBuilder.newJob(CacheWarmupJob.class)
                    .withIdentity("manualWarmupJob" + System.currentTimeMillis())
                    .usingJobData(dataMap)
                    .build();

            Trigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity("manualWarmupTrigger" + System.currentTimeMillis())
                    .startNow()
                    .build();

            scheduler.scheduleJob(jobDetail, trigger);

            log.info("手动缓存预热使用 fullLoad 触发:{}", fullLoad);
            return ResultDTO.success("Cache warmup triggered successfully");
        } catch (Exception e) {
            log.error("触发缓存预热失败", e);
            return ResultDTO.fail("Failed to trigger cache warmup: " + e.getMessage());
        }
    }

    @GetMapping("/status")
    public ResultDTO<Map<String, Object>> getStatus() {
        try {
            Map<String, Object> status = new HashMap<>();

            // 获取Quartz调度器状态
            status.put("schedulerRunning", !scheduler.isInStandbyMode());
            status.put("schedulerName", scheduler.getSchedulerName());

            // 获取任务信息
            status.put("warmupJobExists", scheduler.checkExists(cacheWarmupJobDetail.getKey()));

            // 获取触发器信息
            Trigger trigger = scheduler.getTrigger(TriggerKey.triggerKey("cacheWarmupTrigger"));
            if (trigger != null) {
                status.put("nextFireTime", trigger.getNextFireTime());
                status.put("previousFireTime", trigger.getPreviousFireTime());

                if (trigger instanceof CronTrigger) {
                    status.put("cronExpression", ((CronTrigger) trigger).getCronExpression());
                }
            }

            return ResultDTO.success(status);
        } catch (Exception e) {
            log.error("获取缓存状态失败", e);
            return ResultDTO.fail("Failed to get cache status: " + e.getMessage());
        }
    }

    @PostMapping("/pause")
    public ResultDTO<String> pauseScheduler() {
        try {
            scheduler.standby();
            log.info("缓存预热调度器暂停");
            return ResultDTO.success("Cache warmup scheduler paused successfully");
        } catch (Exception e) {
            log.error("暂停缓存预热调度失败", e);
            return ResultDTO.fail("Failed to pause scheduler: " + e.getMessage());
        }
    }

    @PostMapping("/resume")
    public ResultDTO<String> resumeScheduler() {
        try {
            scheduler.start();
            log.info("缓存预热调度器恢复");
            return ResultDTO.success("Cache warmup scheduler resumed successfully");
        } catch (Exception e) {
            log.error("恢复缓存预热调度器失败", e);
            return ResultDTO.fail("Failed to resume scheduler: " + e.getMessage());
        }
    }
}