package com.iptv.flux.service.controller;

import com.iptv.flux.common.constants.UserGroupConstants;
import com.iptv.flux.common.dto.GroupInfoDTO;
import com.iptv.flux.common.dto.ResultDTO;
import com.iptv.flux.common.dto.UserGroupDTO;
import com.iptv.flux.common.swagger.SwaggerAnnotations;
import com.iptv.flux.common.utils.MetricsRegistryUtil;
import com.iptv.flux.service.service.GroupInfoService;
import com.iptv.flux.service.service.UserGroupService;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.ratelimiter.annotation.RateLimiter;
import io.github.resilience4j.timelimiter.annotation.TimeLimiter;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.controller
 * @className: UserGroupController
 * @author: chiron
 * @description: 用户分组控制器
 * @date: 2025/2/28 12:58
 * @version: 1.0
 */
@RestController
@RequestMapping(UserGroupConstants.API_BASE_PATH)
@RequiredArgsConstructor
@Slf4j
@Validated
@Tag(name = "用户分组管理", description = "用户分组查询与管理接口")
public class UserGroupController {

    private final UserGroupService userGroupService;
    private final GroupInfoService groupInfoService;
    private final MetricsRegistryUtil metricsRegistry;

    /**
     * 获取用户分组信息
     *
     * @param source 请求来源
     * @param userId 用户ID
     * @return 包含分组ID集合的结果
     */
    @GetMapping(value = "/{source}/{userId}", produces = MediaType.APPLICATION_JSON_VALUE)
    @CircuitBreaker(name = UserGroupConstants.CIRCUIT_BREAKER_NAME, fallbackMethod = "getUserGroupFallback")
    @TimeLimiter(name = UserGroupConstants.CIRCUIT_BREAKER_NAME)
    @RateLimiter(name = UserGroupConstants.RATE_LIMITER_NAME)
    @Timed(value = "api.usergroup.get", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "获取用户分组信息",
            description = "根据来源和用户ID获取该用户所属的所有分组ID",
            responseType = Set.class
    )
    public CompletableFuture<ResultDTO<Set<String>>> getUserGroup(
            @PathVariable @NotBlank String source,
            @PathVariable @NotBlank String userId,
            HttpServletRequest request) {

        String clientIp = getClientIp(request);
        long startTime = System.currentTimeMillis();
        log.info("流量平台-----> 收到获取用户分组请求。来源: {}, 用户ID: {}, 客户端IP: {}",
                source, userId, clientIp);

        metricsRegistry.incrementCounter(UserGroupConstants.QUERY_TOTAL_METRIC);

        return CompletableFuture.supplyAsync(() -> {
            try {
                Set<String> groups = userGroupService.loadUserGroupInfo(source, userId, clientIp);

                long executionTime = System.currentTimeMillis() - startTime;
                log.info("流量平台-----> 找到来源: {}, 用户ID: {}, 客户端IP: {} 的 {} 个分组，耗时 {}ms",
                        source, userId, clientIp, groups.size(), executionTime);

                return ResultDTO.success(groups);
            } catch (Exception e) {
                log.error("流量平台-----> 加载来源: {}, 用户ID: {}, 客户端IP: {} 的用户分组出错",
                        source, userId, clientIp, e);
                metricsRegistry.incrementCounter(UserGroupConstants.QUERY_ERROR_METRIC);
                throw e;
            }
        });
    }

    /**
     * 获取详细的用户分组信息，包含分组详情
     *
     * @param source 请求来源
     * @param userId 用户ID
     * @return 包含分组ID到分组信息映射的结果
     */
    @GetMapping(value = "/{source}/{userId}/detail", produces = MediaType.APPLICATION_JSON_VALUE)
    @CircuitBreaker(name = UserGroupConstants.CIRCUIT_BREAKER_NAME, fallbackMethod = "getUserGroupDetailFallback")
    @TimeLimiter(name = UserGroupConstants.CIRCUIT_BREAKER_NAME)
    @RateLimiter(name = UserGroupConstants.RATE_LIMITER_NAME)
    @Timed(value = "api.usergroup.detail.get", percentiles = {0.5, 0.95, 0.99})
    public CompletableFuture<ResultDTO<Map<String, GroupInfoDTO>>> getUserGroupDetail(
            @PathVariable @NotBlank String source,
            @PathVariable @NotBlank String userId,
            HttpServletRequest request) {

        String clientIp = getClientIp(request);
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 收到获取详细用户分组请求，来源: {}, 用户ID: {}, IP: {}",
                source, userId, clientIp);

        metricsRegistry.incrementCounter(UserGroupConstants.QUERY_TOTAL_METRIC, "detail", "true");

        return CompletableFuture.supplyAsync(() -> {
            try {
                Set<String> groupIds = userGroupService.loadUserGroupInfo(source, userId, clientIp);

                if (groupIds.isEmpty()) {
                    log.debug("流量平台-----> 来源: {}, 用户ID: {} 未找到分组", source, userId);
                    return ResultDTO.success(Collections.emptyMap());
                }

                Map<String, GroupInfoDTO> groupDetails = groupInfoService.getGroupInfoBatch(List.copyOf(groupIds));

                log.debug("流量平台-----> 来源: {}, 用户ID: {} 找到 {} 个分组详情，耗时 {}ms",
                        source, userId, groupDetails.size(), System.currentTimeMillis() - startTime);

                return ResultDTO.success(groupDetails);
            } catch (Exception e) {
                log.error("流量平台-----> 加载来源: {}, 用户ID: {} 的详细用户分组出错", source, userId, e);
                metricsRegistry.incrementCounter(UserGroupConstants.QUERY_ERROR_METRIC, "detail", "true");
                throw e;
            }
        });
    }

    /**
     * 保存用户分组信息
     *
     * @param userGroupDTO 用户分组数据
     * @return 成功响应
     */
    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.save", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.SaveOperation(
            summary = "保存用户分组信息",
            description = "保存用户与分组的关联关系"
    )
    public ResultDTO<String> saveUserGroup(@RequestBody @Valid UserGroupDTO userGroupDTO) {
        log.info("流量平台-----> 正在保存用户ID: {}, 来源: {}, 分组数: {} 的用户分组",
                userGroupDTO.getUserId(), userGroupDTO.getSource(), userGroupDTO.getGroupIds().size());

        userGroupService.saveUserGroupInfo(userGroupDTO);
        return ResultDTO.success("操作成功");
    }

    /**
     * 保存分组信息
     *
     * @param groupInfoDTO 分组信息数据
     * @return 成功响应
     */
    @PostMapping(value = "/group", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.group.save", percentiles = {0.5, 0.95, 0.99})
    public ResultDTO<String> saveGroupInfo(@RequestBody @Valid GroupInfoDTO groupInfoDTO) {
        log.info("流量平台-----> 正在保存分组ID: {}, 业务ID: {} 的分组信息",
                groupInfoDTO.getGroupId(), groupInfoDTO.getBusinessId());

        groupInfoService.saveGroupInfo(groupInfoDTO);
        return ResultDTO.success("操作成功");
    }



    /**
     * getUserGroup的回退方法
     */
    public CompletableFuture<ResultDTO<Set<String>>> getUserGroupFallback(
            String source, String userId, Throwable ex) {
        log.error("流量平台-----> getUserGroup回退，来源: {}, 用户ID: {}", source, userId, ex);
        metricsRegistry.incrementCounter("fallback.usergroup");
        return CompletableFuture.completedFuture(ResultDTO.success(Collections.emptySet()));
    }

    /**
     * getUserGroupDetail的回退方法
     */
    public CompletableFuture<ResultDTO<Map<String, GroupInfoDTO>>> getUserGroupDetailFallback(
            String source, String userId, Throwable ex) {
        log.error("流量平台-----> getUserGroupDetail回退，来源: {}, 用户ID: {}", source, userId, ex);
        metricsRegistry.incrementCounter("fallback.usergroup.detail");
        return CompletableFuture.completedFuture(ResultDTO.success(Collections.emptyMap()));
    }

    // 添加获取客户端IP的辅助方法
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        // 如果是多级代理，取第一个IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }

        return ip;
    }

    /** 20250526 add controller **/

    /**
     * 获取用户分组列表（分页）
     */
    @GetMapping(value = "/list", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.list", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "获取用户分组列表",
            description = "分页获取用户分组列表，可选择按来源和用户ID筛选",
            responseType = Map.class
    )
    public ResponseEntity<ResultDTO<Map<String, Object>>> getUserGroupList(
            @RequestParam(required = false) String source,
            @RequestParam(required = false) String userId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize) {

        log.info("流量平台-----> 收到获取用户分组列表请求。来源: {}, 用户ID: {}, 页码: {}, 每页大小: {}",
                source, userId, page, pageSize);

        // 参数校验
        if (page < 1) {
            page = 1;
        }
        if (pageSize < 1 || pageSize > 100) {
            pageSize = 10;
        }

        try {
            Map<String, Object> result = userGroupService.getUserGroupList(source, userId, page, pageSize);
            return ResponseEntity
                    .ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(ResultDTO.success(result));
        } catch (Exception e) {
            log.error("流量平台-----> 获取用户分组列表出错", e);
            metricsRegistry.incrementCounter(UserGroupConstants.QUERY_ERROR_METRIC);
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(ResultDTO.fail("获取用户分组列表失败：" + e.getMessage()));
        }
    }


    /**
     * 删除用户分组
     */
    @DeleteMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.delete", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "删除用户分组",
            description = "根据ID删除用户分组关系",
            responseType = String.class
    )
    public ResponseEntity<ResultDTO<String>> deleteUserGroup(@PathVariable Long id) {
        log.info("流量平台-----> 收到删除用户分组请求。ID: {}", id);

        try {
            userGroupService.deleteUserGroup(id);
            return ResponseEntity
                    .ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(ResultDTO.success("删除成功"));
        } catch (Exception e) {
            log.error("流量平台-----> 删除用户分组出错，ID: {}", id, e);
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(ResultDTO.fail("删除用户分组失败：" + e.getMessage()));
        }
    }


    /**
     * 获取所有分组
     *
     * @return 分组列表
     */
    @GetMapping(value = "/groups", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.groups.getAll", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "获取所有分组",
            description = "获取系统中所有的分组信息",
            responseType = List.class
    )
    public ResultDTO<List<GroupInfoDTO>> getAllGroups() {
        log.info("流量平台-----> 收到获取所有分组请求");

        try {
            List<GroupInfoDTO> groups = groupInfoService.getAllGroups();
            return ResultDTO.success(groups);
        } catch (Exception e) {
            log.error("流量平台-----> 获取所有分组出错", e);
            return ResultDTO.fail("获取分组信息失败：" + e.getMessage());
        }
    }

    /**
     * 根据运营商获取分组
     *
     * @param source 运营商/来源
     * @return 分组列表
     */
    @GetMapping(value = "/groups/{source}", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.groups.getBySource", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "获取指定运营商的分组",
            description = "根据运营商/来源获取相关的分组信息",
            responseType = List.class
    )
    public ResultDTO<List<GroupInfoDTO>> getGroupsBySource(@PathVariable String source) {
        log.info("流量平台-----> 收到获取运营商分组请求。来源: {}", source);

        try {
            List<GroupInfoDTO> groups = groupInfoService.getGroupsBySource(source);
            return ResultDTO.success(groups);
        } catch (Exception e) {
            log.error("流量平台-----> 获取运营商分组出错，来源: {}", source, e);
            return ResultDTO.fail("获取运营商分组失败：" + e.getMessage());
        }
    }

    /**
     * 健康检查端点 - 兼容性保留
     *
     * @return 状态消息
     */
    @GetMapping(value = "/health", produces = MediaType.APPLICATION_JSON_VALUE)
    @SwaggerAnnotations.QueryOperation(
            summary = "用户分组服务健康检查",
            description = "检查用户分组服务的健康状态，建议使用 /api/monitor/health 获取完整系统状态",
            responseType = String.class
    )
    public ResultDTO<String> healthCheck() {
        log.debug("流量平台-----> 收到用户分组健康检查请求");
        return ResultDTO.success("用户分组服务运行正常");
    }

}