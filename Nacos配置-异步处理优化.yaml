# 在您现有的Nacos配置基础上，添加/更新以下配置项以解决异步处理网络超时问题

# ==================== 服务器配置 ====================
server:
  tomcat:
    max-threads: 1000
    max-connections: 20000
    accept-count: 5000
    connection-timeout: 60000  # 连接超时60秒
    # 文件上传相关配置
    max-http-form-post-size: 272629760  # 260MB
    max-save-post-size: 272629760       # 260MB

# ==================== Spring Boot 文件上传配置 ====================
spring:
  servlet:
    multipart:
      max-file-size: 250MB
      max-request-size: 260MB
      file-size-threshold: 10MB
      location: ${java.io.tmpdir}
      enabled: true
      resolve-lazily: false
  quartz:
    job-store-type: jdbc
    jdbc:
      initialize-schema: always
    properties:
      org.quartz.jobStore.driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate

# ==================== 监控配置 ====================
management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus,metrics
  metrics:
    distribution:
      percentiles-histogram:
        http.server.requests: true
      sla:
        http.server.requests: 50ms,100ms,200ms,500ms
  prometheus:
    metrics:
      export:
        enabled: true

# ==================== 熔断器配置 ====================
resilience4j:
  circuitbreaker:
    instances:
      userGroupService:
        slidingWindowType: TIME_BASED
        slidingWindowSize: 10
        minimumNumberOfCalls: 500
        failureRateThreshold: 30
        slowCallRateThreshold: 25
        slowCallDurationThreshold: 200ms
        waitDurationInOpenState: 5s
        permittedNumberOfCallsInHalfOpenState: 50
        registerHealthIndicator: true
  timelimiter:
    instances:
      userGroupService:
        timeoutDuration: 500ms
  ratelimiter:
    instances:
      userGroupRateLimiter:
        registerHealthIndicator: true
        limitForPeriod: 20000
        limitRefreshPeriod: 1s
        timeoutDuration: 500ms

# ==================== 缓存配置 ====================
cache:
  caffeine:
    initial-capacity: 50000
    maximum-size: 1000000 
    expire-after-write: 72000
    expire-after-access: 43200
  bloom-filter:
    expected-insertions: 10000000
    false-probability: 0.0001
  preload:
    enabled: true
    delay-seconds: 60
  refresh:
    cron: "0 0 2 * * ?"
    enabled: true
    batch-size: 5000
    rebuild-bloom: true 
  warmup:
    enabled: true
    cron: "0 0 3 * * ?"
    batch-size: 1000
    max-batches: 50
    full-load: false
    max-time-minutes: 60
    max-failed-batches: 5

# ==================== 用户组配置 ====================
user-group:
  async:
    core-pool-size: 50
    max-pool-size: 200
    queue-capacity: 1000
    keep-alive: 60
  redis:
    key-prefix: ug
    ttl: 86400
  cache:
    preload:
      enabled: true
      delay-seconds: 60
    old-version-ttl: 3600
  bloom-filter:
    mode: MONITOR_ONLY 
  security:
    ip-threshold: 100
    patterns: "source\\d+:user\\d+,app\\d+:user\\d+"
    rate-limit:
      limit-per-second: 50000
      timeout-ms: 100
    missing-keys-cache:
      size: 10000
      expire-minutes: 1440
  # 数据导入配置 - 优化异步处理和超时设置
  data-import:
    # 业务层面的文件大小限制
    max-file-size: 200MB
    # 批处理大小
    batch-size: 1000
    # 是否启用异步处理
    async-enabled: true
    # 临时文件清理间隔（分钟）
    temp-file-cleanup-interval: 30
    # 支持的文件扩展名
    allowed-extensions: ".xlsx,.xls"
    # 默认数据来源
    default-source: "dx"
    # 异步处理配置
    async:
      # 核心线程数 - 增加以支持更多并发导入
      core-pool-size: 10
      # 最大线程数 - 增加以处理大量数据
      max-pool-size: 20
      # 队列容量 - 增加以缓冲更多任务
      queue-capacity: 500
      # 线程保持活跃时间（秒）- 增加以减少线程创建销毁开销
      keep-alive: 300
    # HTTP超时配置
    http:
      # 连接超时时间（毫秒）- 增加以支持大文件上传
      connect-timeout: 60000
      # 读取超时时间（毫秒）- 增加以支持大数据量处理
      read-timeout: 300000
    # 任务超时配置
    task:
      # 单个任务最大执行时间（分钟）
      max-execution-minutes: 30
      # 进度更新间隔（秒）
      progress-update-interval: 10
      # 批处理超时时间（秒）
      batch-timeout: 60

# ==================== 日志配置 ====================
logging:
  level:
    root: INFO
    com.iptv.flux: INFO
    org.springframework.web: INFO
    # 文件上传相关日志
    org.springframework.web.multipart: DEBUG
    org.apache.tomcat.util.http.fileupload: DEBUG
    # 数据导入相关日志
    com.iptv.flux.service.controller.DataImportController: DEBUG
    com.iptv.flux.service.service.DataImportService: DEBUG
    # 异步处理相关日志
    org.springframework.scheduling.concurrent: DEBUG

# ==================== Swagger配置 ====================
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
    disable-swagger-default-url: true
    default-models-expand-depth: 3
  group-configs:
    - group: default
      paths-to-match: /**
