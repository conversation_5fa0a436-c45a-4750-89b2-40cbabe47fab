-- 创建 Nacos 数据库及表
CREATE DATABASE IF NOT EXISTS nacos DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户分组数据库
CREATE DATABASE IF NOT EXISTS flux_manager DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE usergroup;

-- 创建用户分组关系表
CREATE TABLE IF NOT EXISTS `user_group_relation` (
                                                     `id` bigint NOT NULL AUTO_INCREMENT,
                                                     `user_id` varchar(64) NOT NULL COMMENT '用户ID',
    `source` varchar(32) NOT NULL COMMENT '来源标识',
    `group_ids` longtext NOT NULL COMMENT '分组IDs，逗号分隔',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_source` (`user_id`,`source`),
    KEY `idx_source` (`source`),
    KEY `idx_user_id` (`user_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户分组关系表';

-- 创建分组信息表
CREATE TABLE IF NOT EXISTS `group_info` (
                                            `id` bigint NOT NULL AUTO_INCREMENT,
                                            `group_id` varchar(64) NOT NULL COMMENT '分组ID',
    `business_id` varchar(64) NOT NULL COMMENT '业务ID',
    `group_name` varchar(128) NOT NULL COMMENT '分组名称',
    `description` varchar(512) DEFAULT NULL COMMENT '分组描述',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_group_id` (`group_id`),
    KEY `idx_business_id` (`business_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分组信息表';

-- 添加数据库优化设置
SET GLOBAL max_connections = 2000;
SET GLOBAL innodb_buffer_pool_size = 4294967296; -- 4GB
SET GLOBAL innodb_flush_log_at_trx_commit = 2;
SET GLOBAL innodb_file_per_table = 1;
SET GLOBAL innodb_flush_method = 'O_DIRECT';
SET GLOBAL innodb_buffer_pool_instances = 8;